/**
 * WPMU Dev Session Utilities
 * 
 * High-level utilities for managing WPMU Dev sessions across automations
 */

const logger = require('./logger');
const WpmuBrowserManager = require('./wpmuBrowserManager');
const { loginToWpmuDev } = require('../automations/wpmu-hub/login');

/**
 * Get an authenticated WPMU Dev browser session
 * @param {Object} options - Configuration options
 * @param {boolean} options.headless - Run in headless mode (default: false for 2FA)
 * @param {boolean} options.forceLogin - Force a fresh login even if session exists
 * @returns {Promise<WpmuBrowserManager>} Authenticated browser manager
 */
async function getAuthenticatedSession(options = {}) {
  const { headless = false, forceLogin = false } = options;

  logger.info('Getting authenticated WPMU Dev session', { headless, forceLogin });

  const browserManager = new WpmuBrowserManager({ headless });
  await browserManager.initialize();

  try {
    let isLoggedIn = false;
    
    if (!forceLogin) {
      isLoggedIn = await browserManager.isLoggedIn();
    }

    if (!isLoggedIn) {
      logger.info('Session not authenticated, performing login');
      const loginResult = await loginToWpmuDev({ 
        browserManager,
        headless 
      });
      
      if (!loginResult.success) {
        await browserManager.close();
        throw new Error('Failed to authenticate: ' + loginResult.message);
      }
    } else {
      logger.info('Session already authenticated');
    }

    return browserManager;
  } catch (error) {
    await browserManager.close();
    throw error;
  }
}

/**
 * Execute a function with an authenticated WPMU Dev session
 * @param {Function} fn - Function to execute with the browser manager
 * @param {Object} options - Configuration options
 * @param {boolean} options.headless - Run in headless mode (default: false)
 * @param {boolean} options.forceLogin - Force a fresh login
 * @returns {Promise<any>} Result of the executed function
 */
async function withAuthenticatedSession(fn, options = {}) {
  const browserManager = await getAuthenticatedSession(options);
  
  try {
    return await fn(browserManager);
  } finally {
    await browserManager.close();
  }
}

/**
 * Check if there's a valid WPMU Dev session without opening a browser
 * @returns {Promise<boolean>} True if session exists and might be valid
 */
async function hasStoredSession() {
  const path = require('path');
  const fs = require('fs').promises;
  
  try {
    const contextPath = path.join(__dirname, '..', 'data', 'wpmu-context');
    const storageStatePath = path.join(contextPath, 'storage-state.json');
    
    await fs.access(storageStatePath);
    const stats = await fs.stat(storageStatePath);
    
    // Check if the session file is recent (less than 24 hours old)
    const ageInHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
    
    logger.info('Found stored session', { 
      ageInHours: Math.round(ageInHours * 100) / 100,
      path: storageStatePath 
    });
    
    return ageInHours < 24;
  } catch (error) {
    logger.info('No stored session found');
    return false;
  }
}

/**
 * Clear stored WPMU Dev session
 * @returns {Promise<void>}
 */
async function clearStoredSession() {
  const path = require('path');
  const fs = require('fs').promises;
  
  try {
    const contextPath = path.join(__dirname, '..', 'data', 'wpmu-context');
    const storageStatePath = path.join(contextPath, 'storage-state.json');
    
    await fs.unlink(storageStatePath);
    logger.info('Stored session cleared');
  } catch (error) {
    logger.info('No stored session to clear');
  }
}

module.exports = {
  getAuthenticatedSession,
  withAuthenticatedSession,
  hasStoredSession,
  clearStoredSession
};
