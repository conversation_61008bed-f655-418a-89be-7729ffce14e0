/**
 * WPMU Dev Browser Manager
 * 
 * Manages persistent browser context and session state for WPMU Dev automations
 */

const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs').promises;
const logger = require('./logger');

class WpmuBrowserManager {
  constructor(options = {}) {
    this.browser = null;
    this.context = null;
    this.page = null;
    this.contextPath = options.contextPath || path.join(__dirname, '..', 'data', 'wpmu-context');
    this.headless = options.headless !== undefined ? options.headless : false;
    this.sessionTimeout = options.sessionTimeout || 30 * 60 * 1000; // 30 minutes default
  }

  /**
   * Initialize browser with persistent context
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      logger.info('Initializing WPMU browser manager');
      
      // Ensure context directory exists
      await this.ensureContextDirectory();
      
      // Launch browser
      this.browser = await chromium.launch({ 
        headless: this.headless,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      // Create or restore persistent context
      this.context = await this.browser.newContext({
        storageState: await this.getStorageState()
      });
      
      // Create new page
      this.page = await this.context.newPage();
      
      logger.info('Browser manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize browser manager', error);
      throw error;
    }
  }

  /**
   * Check if user is currently logged into WPMU Dev
   * @returns {Promise<boolean>}
   */
  async isLoggedIn() {
    try {
      if (!this.page) {
        throw new Error('Browser not initialized. Call initialize() first.');
      }

      logger.info('Checking WPMU Dev login status');
      
      // Navigate to WPMU Dev Hub
      await this.page.goto('https://wpmudev.com/hub2/', { 
        waitUntil: 'networkidle',
        timeout: 15000 
      });

      // Wait a moment for any redirects
      await this.page.waitForTimeout(2000);

      const currentUrl = this.page.url();
      logger.info('Current URL after navigation', { url: currentUrl });

      // Check for login redirect
      if (currentUrl.includes('/login/')) {
        logger.info('Redirected to login page - not logged in');
        return false;
      }

      // Check for login form elements
      const loginSubmitButton = await this.page.$('#wpmud-login-submit');
      if (loginSubmitButton) {
        logger.info('Login form detected - not logged in');
        return false;
      }

      // Check for authenticated elements
      const hubNavigation = await this.page.$('.hub-navigation');
      const userMenu = await this.page.$('[data-test="user-menu"]');
      const dashboardElements = await this.page.$('.wpmudev-dashboard');

      if (hubNavigation || userMenu || dashboardElements) {
        logger.info('Authenticated elements found - logged in');
        await this.saveStorageState();
        return true;
      }

      logger.info('No clear authentication indicators found - assuming not logged in');
      return false;
    } catch (error) {
      logger.error('Error checking login status', error);
      return false;
    }
  }

  /**
   * Get current page instance
   * @returns {Page}
   */
  getPage() {
    if (!this.page) {
      throw new Error('Browser not initialized. Call initialize() first.');
    }
    return this.page;
  }

  /**
   * Get current browser context
   * @returns {BrowserContext}
   */
  getContext() {
    if (!this.context) {
      throw new Error('Browser not initialized. Call initialize() first.');
    }
    return this.context;
  }

  /**
   * Save current storage state to disk
   * @returns {Promise<void>}
   */
  async saveStorageState() {
    try {
      if (!this.context) {
        throw new Error('Browser context not available');
      }

      const storageState = await this.context.storageState();
      const storageStatePath = path.join(this.contextPath, 'storage-state.json');
      
      await fs.writeFile(storageStatePath, JSON.stringify(storageState, null, 2));
      logger.info('Storage state saved', { path: storageStatePath });
    } catch (error) {
      logger.error('Failed to save storage state', error);
    }
  }

  /**
   * Close browser and cleanup
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (this.context) {
        await this.saveStorageState();
      }
      
      if (this.browser) {
        await this.browser.close();
        logger.info('Browser closed successfully');
      }
    } catch (error) {
      logger.error('Error closing browser', error);
    } finally {
      this.browser = null;
      this.context = null;
      this.page = null;
    }
  }

  /**
   * Ensure context directory exists
   * @private
   */
  async ensureContextDirectory() {
    try {
      await fs.access(this.contextPath);
    } catch {
      await fs.mkdir(this.contextPath, { recursive: true });
      logger.info('Created context directory', { path: this.contextPath });
    }
  }

  /**
   * Get storage state from disk or return undefined for new session
   * @private
   * @returns {Promise<Object|undefined>}
   */
  async getStorageState() {
    try {
      const storageStatePath = path.join(this.contextPath, 'storage-state.json');
      const storageStateData = await fs.readFile(storageStatePath, 'utf8');
      const storageState = JSON.parse(storageStateData);
      
      logger.info('Loaded existing storage state');
      return storageState;
    } catch (error) {
      logger.info('No existing storage state found, starting fresh session');
      return undefined;
    }
  }
}

module.exports = WpmuBrowserManager;
