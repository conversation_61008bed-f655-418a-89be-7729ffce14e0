{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-05T16:57:20.515Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-05T16:57:41.002Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-05T16:57:41.004Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-05T16:57:41.004Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-05T17:09:28.666Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:11:48.718Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:13.122Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:32.115Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:49.211Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:56.477Z"}
{"city":"bordeaux","level":"info","message":"Starting createNewSite workflow","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"city":"bordeaux","level":"info","message":"Starting createNewSite workflow","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"level":"info","message":"Step 1: Cloning template site","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"city":"bordeaux","level":"info","message":"Starting site cloning process","service":"work-automations","templateSite":"wptemplate.impacthub.net","timestamp":"2025-06-05T17:12:56.481Z"}
{"cmd":"lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Shared-ImpactHub/WPMU Dev Login Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-05T17:12:56.917Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:36:22.298Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:22.299Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:36:22.299Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:36:46.951Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:46.953Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:36:46.953Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:09.542Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:31.782Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T14:51:12.960Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Tech Team/Websites/wpmudev.com Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.272Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.273Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T14:53:53.320Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: undefined Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.654Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.655Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:57:12.640Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:57:12.641Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:57:12.641Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:05:22.739Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:05:23.124Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:05:23.125Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:09:09.806Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.292Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.318Z"}
{"cmd":"lpass show --field=\"Email\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Email from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Email\" \"5530442257124319668\"\nError: Could not find specified field 'Email'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Email\" \"5530442257124319668\"\nError: Could not find specified field 'Email'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'Email'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.337Z"}
{"cmd":"lpass show --field=\"email\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve email from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"email\" \"5530442257124319668\"\nError: Could not find specified field 'email'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"email\" \"5530442257124319668\"\nError: Could not find specified field 'email'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'email'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.359Z"}
{"cmd":"lpass show --field=\"Login\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Login from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Login\" \"5530442257124319668\"\nError: Could not find specified field 'Login'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Login\" \"5530442257124319668\"\nError: Could not find specified field 'Login'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'Login'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.381Z"}
{"cmd":"lpass show --field=\"login\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve login from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"login\" \"5530442257124319668\"\nError: Could not find specified field 'login'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"login\" \"5530442257124319668\"\nError: Could not find specified field 'login'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'login'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.405Z"}
{"level":"error","message":"Failed to retrieve WPMU Dev credentials No username field found in LastPass entry: 5530442257124319668","service":"work-automations","stack":"Error: No username field found in LastPass entry: 5530442257124319668\n    at getUsername (/Users/<USER>/Projects/work automations/lib/lastpass.js:96:8)\n    at async getCredentials (/Users/<USER>/Projects/work automations/config/credentials.js:74:29)\n    at async Command.<anonymous> (/Users/<USER>/Projects/work automations/main.js:53:26)\n    at async Command.parseAsync (/Users/<USER>/Projects/work automations/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Projects/work automations/main.js:121:3)","timestamp":"2025-06-06T15:09:10.405Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:11:45.275Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:11:45.764Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:11:45.765Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:12:47.707Z"}
{"level":"info","message":"WPMU Dev Credentials retrieved successfully","passwordLength":16,"service":"work-automations","timestamp":"2025-06-06T15:12:48.102Z","username":"<EMAIL>"}
