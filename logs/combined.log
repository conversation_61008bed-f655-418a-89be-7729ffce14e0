{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-05T16:57:20.515Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-05T16:57:41.002Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-05T16:57:41.004Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-05T16:57:41.004Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-05T17:09:28.666Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:11:48.718Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:13.122Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:32.115Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:49.211Z"}
{"level":"info","message":"LastPass CLI validation successful","service":"work-automations","timestamp":"2025-06-05T17:12:56.477Z"}
{"city":"bordeaux","level":"info","message":"Starting createNewSite workflow","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"city":"bordeaux","level":"info","message":"Starting createNewSite workflow","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"level":"info","message":"Step 1: Cloning template site","service":"work-automations","timestamp":"2025-06-05T17:12:56.480Z"}
{"city":"bordeaux","level":"info","message":"Starting site cloning process","service":"work-automations","templateSite":"wptemplate.impacthub.net","timestamp":"2025-06-05T17:12:56.481Z"}
{"cmd":"lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Shared-ImpactHub/WPMU Dev Login Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-05T17:12:56.917Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:36:22.298Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:22.299Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:36:22.299Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:36:46.951Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:46.953Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:36:46.953Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:09.542Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:31.782Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T14:51:12.960Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Tech Team/Websites/wpmudev.com Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.272Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.273Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T14:53:53.320Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: undefined Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.654Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.655Z"}
{"level":"info","message":"Test info message","service":"work-automations","timestamp":"2025-06-06T14:57:12.640Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:57:12.641Z"}
{"level":"warn","message":"Test warning message","service":"work-automations","timestamp":"2025-06-06T14:57:12.641Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:05:22.739Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:05:23.124Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:05:23.125Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:09:09.806Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.292Z"}
{"cmd":"lpass show --field=\"username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"5530442257124319668\"\nError: Could not find specified field 'username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'username'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.318Z"}
{"cmd":"lpass show --field=\"Email\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Email from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Email\" \"5530442257124319668\"\nError: Could not find specified field 'Email'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Email\" \"5530442257124319668\"\nError: Could not find specified field 'Email'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'Email'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.337Z"}
{"cmd":"lpass show --field=\"email\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve email from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"email\" \"5530442257124319668\"\nError: Could not find specified field 'email'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"email\" \"5530442257124319668\"\nError: Could not find specified field 'email'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'email'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.359Z"}
{"cmd":"lpass show --field=\"Login\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Login from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Login\" \"5530442257124319668\"\nError: Could not find specified field 'Login'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Login\" \"5530442257124319668\"\nError: Could not find specified field 'Login'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'Login'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.381Z"}
{"cmd":"lpass show --field=\"login\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve login from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"login\" \"5530442257124319668\"\nError: Could not find specified field 'login'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"login\" \"5530442257124319668\"\nError: Could not find specified field 'login'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified field 'login'.\n","stdout":"","timestamp":"2025-06-06T15:09:10.405Z"}
{"level":"error","message":"Failed to retrieve WPMU Dev credentials No username field found in LastPass entry: 5530442257124319668","service":"work-automations","stack":"Error: No username field found in LastPass entry: 5530442257124319668\n    at getUsername (/Users/<USER>/Projects/work automations/lib/lastpass.js:96:8)\n    at async getCredentials (/Users/<USER>/Projects/work automations/config/credentials.js:74:29)\n    at async Command.<anonymous> (/Users/<USER>/Projects/work automations/main.js:53:26)\n    at async Command.parseAsync (/Users/<USER>/Projects/work automations/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Projects/work automations/main.js:121:3)","timestamp":"2025-06-06T15:09:10.405Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:11:45.275Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve Username from LastPass entry: 5530442257124319668 Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:11:45.764Z"}
{"cmd":"lpass show --field=\"Username\" \"5530442257124319668\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"Username\" \"5530442257124319668\"\nError: Could not find specified field 'Username'.\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified field 'Username'.\n","stdout":"","timestamp":"2025-06-06T15:11:45.765Z"}
{"level":"info","message":"Testing WPMU Dev credential retrieval...","service":"work-automations","timestamp":"2025-06-06T15:12:47.707Z"}
{"level":"info","message":"WPMU Dev Credentials retrieved successfully","passwordLength":16,"service":"work-automations","timestamp":"2025-06-06T15:12:48.102Z","username":"<EMAIL>"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:20:45.964Z"}
{"level":"info","message":"No stored session found","service":"work-automations","timestamp":"2025-06-06T15:20:45.972Z"}
{"hasSession":false,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:20:45.972Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:20:45.973Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:20:45.973Z"}
{"level":"info","message":"No existing storage state found, starting fresh session","service":"work-automations","timestamp":"2025-06-06T15:20:50.127Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:20:52.431Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:20:52.431Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:20:56.987Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:20:56.990Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:20:56.990Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:20:56.991Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:20:56.991Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:21:01.120Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:21:01.120Z"}
{"cmd":"lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"","code":1,"killed":false,"level":"error","message":"WPMU Dev login failed Command failed: lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)\n    at Process.callbackTrampoline (node:internal/async_hooks:130:17)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T15:21:01.858Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:21:02.539Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:21:02.916Z"}
{"cmd":"lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"","code":1,"killed":false,"level":"error","message":"Example WPMU task failed Command failed: lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --username \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)\n    at Process.callbackTrampoline (node:internal/async_hooks:130:17)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T15:21:02.916Z"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:22:05.056Z"}
{"ageInHours":0.02,"level":"info","message":"Found stored session","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:22:05.064Z"}
{"hasSession":true,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:22:05.064Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:22:05.064Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:22:05.064Z"}
{"level":"info","message":"Loaded existing storage state","service":"work-automations","timestamp":"2025-06-06T15:22:06.799Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:22:08.371Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:22:08.372Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:22:12.267Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:22:12.268Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:22:12.270Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:22:12.271Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:22:12.272Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:22:16.157Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:22:16.157Z"}
{"level":"info","message":"Retrieved WPMU Dev credentials","service":"work-automations","timestamp":"2025-06-06T15:22:16.652Z"}
{"level":"info","message":"Login form detected","service":"work-automations","timestamp":"2025-06-06T15:22:16.685Z"}
{"level":"info","message":"Credentials entered","service":"work-automations","timestamp":"2025-06-06T15:22:16.800Z"}
{"level":"info","message":"Login form submitted","service":"work-automations","timestamp":"2025-06-06T15:22:16.911Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:22:21.930Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:22:25.873Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:22:25.874Z"}
{"level":"error","message":"WPMU Dev login failed Login failed - unknown error","service":"work-automations","stack":"Error: Login failed - unknown error\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:165:13)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:22:25.984Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:22:26.474Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:22:26.792Z"}
{"level":"error","message":"Example WPMU task failed Login failed - unknown error","service":"work-automations","stack":"Error: Login failed - unknown error\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:165:13)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:22:26.792Z"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:26:03.677Z"}
{"ageInHours":0.06,"level":"info","message":"Found stored session","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:26:03.685Z"}
{"hasSession":true,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:26:03.686Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:26:03.686Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:26:03.686Z"}
{"level":"info","message":"Loaded existing storage state","service":"work-automations","timestamp":"2025-06-06T15:26:05.598Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:26:06.925Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:26:06.925Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:26:10.596Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:26:10.597Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:26:10.597Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:26:10.598Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:26:10.598Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:26:14.049Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:26:14.049Z"}
{"level":"info","message":"Retrieved WPMU Dev credentials","service":"work-automations","timestamp":"2025-06-06T15:26:14.466Z"}
{"level":"info","message":"Login form detected","service":"work-automations","timestamp":"2025-06-06T15:26:14.493Z"}
{"level":"info","message":"Credentials entered","service":"work-automations","timestamp":"2025-06-06T15:26:14.581Z"}
{"level":"info","message":"Login form submitted","service":"work-automations","timestamp":"2025-06-06T15:26:14.695Z"}
{"level":"error","message":"WPMU Dev login failed page.$: Execution context was destroyed, most likely because of a navigation","name":"Error","service":"work-automations","stack":"page.$: Execution context was destroyed, most likely because of a navigation\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:108:16)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:26:17.723Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:26:18.233Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:26:18.476Z"}
{"level":"error","message":"Example WPMU task failed page.$: Execution context was destroyed, most likely because of a navigation","name":"Error","service":"work-automations","stack":"page.$: Execution context was destroyed, most likely because of a navigation\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:108:16)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:26:18.476Z"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:28:17.557Z"}
{"ageInHours":0.03,"level":"info","message":"Found stored session","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:28:17.564Z"}
{"hasSession":true,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:28:17.564Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:28:17.565Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:28:17.565Z"}
{"level":"info","message":"Loaded existing storage state","service":"work-automations","timestamp":"2025-06-06T15:28:18.657Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:28:20.192Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:28:20.194Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:28:24.060Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:28:24.060Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:28:24.060Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:28:24.061Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:28:24.061Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:28:27.386Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:28:27.386Z"}
{"level":"info","message":"Retrieved WPMU Dev credentials","service":"work-automations","timestamp":"2025-06-06T15:28:27.872Z"}
{"level":"info","message":"Login form detected","service":"work-automations","timestamp":"2025-06-06T15:28:27.895Z"}
{"level":"info","message":"Credentials entered","service":"work-automations","timestamp":"2025-06-06T15:28:27.972Z"}
{"level":"info","message":"Login form submitted","service":"work-automations","timestamp":"2025-06-06T15:28:28.073Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:28:36.438Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:28:40.324Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:28:40.324Z"}
{"currentUrl":"https://wpmudev.com/login/","level":"info","message":"Login verification failed","service":"work-automations","timestamp":"2025-06-06T15:28:40.324Z"}
{"level":"error","message":"WPMU Dev login failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:375:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:28:40.368Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:28:40.802Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:28:41.027Z"}
{"level":"error","message":"Example WPMU task failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:375:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:28:41.027Z"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:31:26.450Z"}
{"ageInHours":0.05,"level":"info","message":"Found stored session","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:31:26.461Z"}
{"hasSession":true,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:31:26.461Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:31:26.462Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:31:26.462Z"}
{"level":"info","message":"Loaded existing storage state","service":"work-automations","timestamp":"2025-06-06T15:31:28.023Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:31:29.299Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:31:29.300Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:31:33.080Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:31:33.081Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:31:33.082Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:31:33.083Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:31:33.083Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:31:36.999Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:31:37.000Z"}
{"level":"info","message":"Retrieved WPMU Dev credentials","service":"work-automations","timestamp":"2025-06-06T15:31:37.434Z"}
{"level":"info","message":"Login form detected","service":"work-automations","timestamp":"2025-06-06T15:31:37.461Z"}
{"level":"info","message":"Credentials entered","service":"work-automations","timestamp":"2025-06-06T15:31:37.540Z"}
{"level":"info","message":"Login form submitted","service":"work-automations","timestamp":"2025-06-06T15:31:37.644Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:31:46.272Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:31:50.171Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:31:50.171Z"}
{"currentUrl":"https://wpmudev.com/login/","level":"info","message":"Login verification failed","service":"work-automations","timestamp":"2025-06-06T15:31:50.171Z"}
{"level":"error","message":"WPMU Dev login failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:409:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:31:50.251Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:31:50.663Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:31:50.935Z"}
{"level":"error","message":"Example WPMU task failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:409:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:31:50.935Z"}
{"level":"info","message":"Starting example WPMU task","service":"work-automations","timestamp":"2025-06-06T15:33:12.363Z"}
{"ageInHours":0.02,"level":"info","message":"Found stored session","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:33:12.372Z"}
{"hasSession":true,"level":"info","message":"Stored session status","service":"work-automations","timestamp":"2025-06-06T15:33:12.372Z"}
{"forceLogin":false,"headless":false,"level":"info","message":"Getting authenticated WPMU Dev session","service":"work-automations","timestamp":"2025-06-06T15:33:12.372Z"}
{"level":"info","message":"Initializing WPMU browser manager","service":"work-automations","timestamp":"2025-06-06T15:33:12.372Z"}
{"level":"info","message":"Loaded existing storage state","service":"work-automations","timestamp":"2025-06-06T15:33:13.627Z"}
{"level":"info","message":"Browser manager initialized successfully","service":"work-automations","timestamp":"2025-06-06T15:33:14.612Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:33:14.613Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:33:19.520Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:33:19.520Z"}
{"level":"info","message":"Session not authenticated, performing login","service":"work-automations","timestamp":"2025-06-06T15:33:19.520Z"}
{"level":"info","message":"Starting WPMU Dev login process","service":"work-automations","timestamp":"2025-06-06T15:33:19.521Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:33:19.521Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:33:23.421Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:33:23.423Z"}
{"level":"info","message":"Retrieved WPMU Dev credentials","service":"work-automations","timestamp":"2025-06-06T15:33:23.851Z"}
{"level":"info","message":"Login form detected","service":"work-automations","timestamp":"2025-06-06T15:33:23.880Z"}
{"level":"info","message":"Credentials entered","service":"work-automations","timestamp":"2025-06-06T15:33:23.923Z"}
{"level":"info","message":"Login form submitted","service":"work-automations","timestamp":"2025-06-06T15:33:24.025Z"}
{"level":"info","message":"Login verification attempt 1/3","service":"work-automations","timestamp":"2025-06-06T15:33:29.405Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:33:29.407Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:33:33.295Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:33:33.295Z"}
{"level":"info","message":"Current URL during verification: https://wpmudev.com/login/","service":"work-automations","timestamp":"2025-06-06T15:33:33.296Z"}
{"level":"info","message":"Login verification attempt 2/3","service":"work-automations","timestamp":"2025-06-06T15:33:35.296Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:33:35.297Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:33:39.171Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:33:39.171Z"}
{"level":"info","message":"Current URL during verification: https://wpmudev.com/login/","service":"work-automations","timestamp":"2025-06-06T15:33:39.172Z"}
{"level":"info","message":"Login verification attempt 3/3","service":"work-automations","timestamp":"2025-06-06T15:33:41.174Z"}
{"level":"info","message":"Checking WPMU Dev login status","service":"work-automations","timestamp":"2025-06-06T15:33:41.174Z"}
{"level":"info","message":"Current URL after navigation","service":"work-automations","timestamp":"2025-06-06T15:33:45.287Z","url":"https://wpmudev.com/login/"}
{"level":"info","message":"Redirected to login page - not logged in","service":"work-automations","timestamp":"2025-06-06T15:33:45.287Z"}
{"level":"info","message":"Current URL during verification: https://wpmudev.com/login/","service":"work-automations","timestamp":"2025-06-06T15:33:45.287Z"}
{"currentUrl":"https://wpmudev.com/login/","level":"info","message":"Login verification failed","service":"work-automations","timestamp":"2025-06-06T15:33:45.287Z"}
{"level":"error","message":"WPMU Dev login failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:499:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:33:45.336Z"}
{"level":"info","message":"Storage state saved","path":"/Users/<USER>/Projects/work automations/data/wpmu-context/storage-state.json","service":"work-automations","timestamp":"2025-06-06T15:33:45.766Z"}
{"level":"info","message":"Browser closed successfully","service":"work-automations","timestamp":"2025-06-06T15:33:46.000Z"}
{"level":"error","message":"Example WPMU task failed Login failed - unknown error (still on login page) - Username field is empty - Password field is empty","service":"work-automations","stack":"Error: Login failed - unknown error (still on login page) - Username field is empty - Password field is empty\n    at loginToWpmuDev (/Users/<USER>/Projects/work automations/automations/wpmu-hub/login.js:499:10)\n    at async getAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:35:27)\n    at async withAuthenticatedSession (/Users/<USER>/Projects/work automations/lib/wpmuSession.js:64:26)\n    at async exampleWpmuTask (/Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:36:18)\n    at async /Users/<USER>/Projects/work automations/automations/wpmu-hub/sessionExample.js:139:19","timestamp":"2025-06-06T15:33:46.000Z"}
