/**
 * WPMU Hub - Login Block
 *
 * Standalone login automation for WPMU Dev that can be reused across automations.
 * Handles username/password entry, 2FA detection, and session persistence.
 */

const logger = require('../../lib/logger');
const { getWpmuDevCredentials } = require('../../lib/credentials');
const WpmuBrowserManager = require('../../lib/wpmuBrowserManager');

/**
 * Login to WPMU Dev Hub
 * @param {Object} options - Configuration options
 * @param {WpmuBrowserManager} options.browserManager - Optional existing browser manager
 * @param {boolean} options.headless - Run in headless mode (default: false for 2FA)
 * @param {number} options.timeout - Login timeout in milliseconds (default: 300000 = 5 minutes)
 * @returns {Promise<Object>} Result object with login status and browser manager
 */
async function loginToWpmuDev(options = {}) {
	const {
		browserManager: existingBrowserManager,
		headless = false, // Default to false for 2FA interaction
		timeout = 300000, // 5 minutes default for manual interactions
	} = options;

	logger.info('Starting WPMU Dev login process');

	let browserManager = existingBrowserManager;
	let shouldCloseBrowser = false;

	try {
		// Create browser manager if not provided
		if (!browserManager) {
			browserManager = new WpmuBrowserManager({ headless });
			await browserManager.initialize();
			shouldCloseBrowser = true;
		}

		const page = browserManager.getPage();

		// Check if already logged in
		const isLoggedIn = await browserManager.isLoggedIn();
		if (isLoggedIn) {
			logger.info('Already logged in to WPMU Dev');
			return {
				success: true,
				message: 'Already logged in',
				browserManager,
				requiresCleanup: shouldCloseBrowser,
			};
		}

		// Get credentials
		const { username, password } = await getWpmuDevCredentials();
		logger.info('Retrieved WPMU Dev credentials');

		// Navigate to login page if not already there
		const currentUrl = page.url();
		if (!currentUrl.includes('/login/')) {
			logger.info('Navigating to WPMU Dev login page');
			await page.goto('https://wpmudev.com/login/', {
				waitUntil: 'networkidle',
				timeout: 15000,
			});
		}

		// Wait for login form to be visible
		await page.waitForSelector('input[name="username"], input[name="log"]', {
			timeout: 10000,
		});
		logger.info('Login form detected');

		// Fill in credentials - try different possible field names
		const usernameField =
			(await page.$('input[name="username"]')) ||
			(await page.$('input[name="log"]'));
		const passwordField =
			(await page.$('input[name="password"]')) ||
			(await page.$('input[name="pwd"]'));

		if (!usernameField || !passwordField) {
			throw new Error('Could not find username or password fields');
		}

		await usernameField.fill(username);
		await passwordField.fill(password);
		logger.info('Credentials entered');

		// Submit the form
		const submitButton =
			(await page.$('#wpmud-login-submit')) ||
			(await page.$('button[type="submit"]')) ||
			(await page.$('input[type="submit"]'));

		if (!submitButton) {
			throw new Error('Could not find login submit button');
		}

		await submitButton.click();
		logger.info('Login form submitted');

		// Wait for navigation or page changes after form submission
		try {
			// Wait for either navigation or load state change
			await Promise.race([
				page.waitForNavigation({ timeout: 5000 }),
				page.waitForLoadState('networkidle', { timeout: 5000 }),
			]);
		} catch (error) {
			logger.info('No immediate navigation detected, checking current page');
		}

		// Wait a bit more for any dynamic content to load
		await page.waitForTimeout(3000);

		// Check for Cloudflare Turnstile challenge (with error handling for destroyed context)
		let cloudflareChallenge = false;
		try {
			cloudflareChallenge =
				(await page.$('.cf-turnstile')) ||
				(await page.$('[data-sitekey]')) ||
				(await page.$('iframe[src*="cloudflare"]')) ||
				(await page.$('.challenge-running')) ||
				(await page.$('#challenge-form')) ||
				(await page.$('.challenge-stage'));
		} catch (error) {
			if (error.message.includes('Execution context was destroyed')) {
				logger.info(
					'Page navigated during Cloudflare check - checking new page',
				);
				// Page has navigated, let's check the new page
				await page.waitForTimeout(2000);
				try {
					cloudflareChallenge =
						(await page.$('.cf-turnstile')) ||
						(await page.$('[data-sitekey]')) ||
						(await page.$('iframe[src*="cloudflare"]')) ||
						(await page.$('.challenge-running')) ||
						(await page.$('#challenge-form')) ||
						(await page.$('.challenge-stage'));
				} catch (secondError) {
					logger.info('Could not check for Cloudflare on new page, continuing');
					cloudflareChallenge = false;
				}
			} else {
				throw error;
			}
		}

		if (cloudflareChallenge) {
			logger.info(
				'Cloudflare Turnstile challenge detected - waiting for user interaction',
			);
			console.log('\n🛡️  Cloudflare Security Challenge Detected');
			console.log(
				'Please complete the security challenge in the browser window.',
			);
			console.log(
				'This may include clicking "I\'m human" or solving a CAPTCHA.',
			);
			console.log(
				`You have ${Math.round(
					timeout / 1000 / 60,
				)} minutes to complete the challenge.`,
			);
			console.log(
				'The automation will continue once you complete the challenge...\n',
			);

			// Wait for Cloudflare challenge completion
			const maxWaitTime = timeout;
			const checkInterval = 2000;
			let waitedTime = 0;

			while (waitedTime < maxWaitTime) {
				await page.waitForTimeout(checkInterval);
				waitedTime += checkInterval;

				// Show progress every 30 seconds
				if (waitedTime % 30000 === 0) {
					const remainingMinutes = Math.round(
						(maxWaitTime - waitedTime) / 1000 / 60,
					);
					console.log(
						`⏱️  Still waiting for challenge completion... ${remainingMinutes} minutes remaining`,
					);
				}

				// Check if challenge is completed by looking for its absence
				const stillHasChallenge =
					(await page.$('.cf-turnstile')) ||
					(await page.$('[data-sitekey]')) ||
					(await page.$('iframe[src*="cloudflare"]')) ||
					(await page.$('.challenge-running')) ||
					(await page.$('#challenge-form'));

				if (!stillHasChallenge) {
					logger.info('Cloudflare challenge completed');
					console.log('✅ Cloudflare challenge completed successfully!');
					break;
				}

				// Check if we've moved to a different page (success)
				const currentUrl = page.url();
				if (
					currentUrl.includes('/hub2/') ||
					currentUrl.includes('/dashboard/')
				) {
					logger.info(
						'Cloudflare challenge completed - redirected to dashboard',
					);
					console.log(
						'✅ Cloudflare challenge completed - redirected to dashboard!',
					);
					break;
				}

				if (waitedTime >= maxWaitTime) {
					throw new Error(
						'Cloudflare challenge timeout - user did not complete challenge in time',
					);
				}
			}

			// Wait a bit more after challenge completion
			await page.waitForTimeout(2000);
		}

		// Check for 2FA requirement (with error handling)
		let twoFactorElement = false;
		try {
			twoFactorElement =
				(await page.$('.two-factor-authentication')) ||
				(await page.$('[data-test="two-factor"]')) ||
				(await page.$('input[name="two_factor_code"]'));
		} catch (error) {
			if (error.message.includes('Execution context was destroyed')) {
				logger.info(
					'Page navigated during 2FA check - likely successful login',
				);
				twoFactorElement = false;
			} else {
				throw error;
			}
		}

		if (twoFactorElement) {
			logger.info('2FA required - waiting for user input');
			console.log('\n🔐 Two-Factor Authentication Required');
			console.log(
				'Please enter your 2FA code in the browser window that just opened.',
			);
			console.log(
				`You have ${Math.round(timeout / 1000 / 60)} minutes to complete 2FA.`,
			);
			console.log(
				'The automation will continue once you complete the 2FA process...\n',
			);

			// Wait for 2FA completion by checking for successful login indicators
			const maxWaitTime = timeout;
			const checkInterval = 2000;
			let waitedTime = 0;

			while (waitedTime < maxWaitTime) {
				await page.waitForTimeout(checkInterval);
				waitedTime += checkInterval;

				// Show progress every 30 seconds
				if (waitedTime % 30000 === 0) {
					const remainingMinutes = Math.round(
						(maxWaitTime - waitedTime) / 1000 / 60,
					);
					console.log(
						`⏱️  Still waiting for 2FA completion... ${remainingMinutes} minutes remaining`,
					);
				}

				// Check if we've successfully logged in
				const currentUrl = page.url();
				if (
					currentUrl.includes('/hub2/') ||
					currentUrl.includes('/dashboard/')
				) {
					logger.info('2FA completed successfully');
					console.log('✅ 2FA completed successfully!');
					break;
				}

				// Check for authenticated elements
				const hubNavigation = await page.$('.hub-navigation');
				const userMenu = await page.$('[data-test="user-menu"]');

				if (hubNavigation || userMenu) {
					logger.info('2FA completed - authenticated elements found');
					console.log('✅ 2FA completed - authenticated elements found!');
					break;
				}

				// Check if still on 2FA page
				const stillOn2FA =
					(await page.$('.two-factor-authentication')) ||
					(await page.$('input[name="two_factor_code"]'));

				if (!stillOn2FA && !currentUrl.includes('/login/')) {
					// Moved away from 2FA page, likely successful
					break;
				}

				if (waitedTime >= maxWaitTime) {
					throw new Error(
						'2FA timeout - user did not complete authentication in time',
					);
				}
			}
		}

		// Final verification of login success
		await page.waitForTimeout(2000);

		// Try to verify login, handling potential navigation issues
		let finalLoginCheck = false;
		let verificationAttempts = 0;
		const maxVerificationAttempts = 3;

		while (!finalLoginCheck && verificationAttempts < maxVerificationAttempts) {
			verificationAttempts++;
			logger.info(
				`Login verification attempt ${verificationAttempts}/${maxVerificationAttempts}`,
			);

			try {
				finalLoginCheck = await browserManager.isLoggedIn();
				if (finalLoginCheck) {
					logger.info('Login verification successful');
					break;
				}
			} catch (error) {
				if (error.message.includes('Execution context was destroyed')) {
					logger.info(
						'Page navigated during login verification - waiting and retrying',
					);
					await page.waitForTimeout(3000);
					continue;
				} else {
					throw error;
				}
			}

			// If not logged in, check if we're on a 2FA page or similar
			const currentUrl = page.url();
			logger.info(`Current URL during verification: ${currentUrl}`);

			// Check if we're on a 2FA page (Google Authenticator, etc.)
			const on2FAPage =
				currentUrl.includes('accounts.google.com') ||
				currentUrl.includes('2fa') ||
				currentUrl.includes('two-factor') ||
				currentUrl.includes('authenticator');

			if (on2FAPage) {
				logger.info(
					'Detected 2FA page - waiting for user to complete authentication',
				);
				console.log('\n🔐 Google Authenticator / 2FA Page Detected');
				console.log(
					'Please complete the authentication in the browser window.',
				);
				console.log(
					`You have ${Math.round(
						timeout / 1000 / 60,
					)} minutes to complete this step.`,
				);
				console.log(
					'The automation will continue once you complete the authentication...\n',
				);

				// Wait for 2FA completion with longer timeout
				const maxWaitTime = timeout;
				const checkInterval = 3000;
				let waitedTime = 0;

				while (waitedTime < maxWaitTime) {
					await page.waitForTimeout(checkInterval);
					waitedTime += checkInterval;

					// Show progress every 30 seconds
					if (waitedTime % 30000 === 0) {
						const remainingMinutes = Math.round(
							(maxWaitTime - waitedTime) / 1000 / 60,
						);
						console.log(
							`⏱️  Still waiting for authentication... ${remainingMinutes} minutes remaining`,
						);
					}

					// Check if we've moved away from 2FA page
					const newUrl = page.url();
					if (
						!newUrl.includes('accounts.google.com') &&
						!newUrl.includes('2fa') &&
						!newUrl.includes('two-factor') &&
						!newUrl.includes('authenticator')
					) {
						logger.info('Moved away from 2FA page, checking login status');
						console.log('✅ Authentication step completed!');
						break;
					}

					if (waitedTime >= maxWaitTime) {
						throw new Error(
							'2FA timeout - user did not complete authentication in time',
						);
					}
				}

				// After 2FA completion, try verification again
				await page.waitForTimeout(3000);
				continue;
			}

			// If we're still on login page after attempts, that's likely a real failure
			if (
				currentUrl.includes('/login/') &&
				verificationAttempts >= maxVerificationAttempts
			) {
				break; // Exit loop to handle error below
			}

			// Wait before next attempt
			await page.waitForTimeout(2000);
		}

		if (!finalLoginCheck) {
			// Get current URL for debugging
			const currentUrl = page.url();
			logger.info('Login verification failed', { currentUrl });

			// Check for various error indicators
			const errorSelectors = [
				'.error-message',
				'.login-error',
				'[data-test="error"]',
				'.notice-error',
				'.wp-die-message',
				'.error',
				'#login_error',
			];

			let errorMessage = 'Login failed - unknown error';

			for (const selector of errorSelectors) {
				const errorElement = await page.$(selector);
				if (errorElement) {
					const text = await errorElement.textContent();
					if (text && text.trim()) {
						errorMessage = `Login failed: ${text.trim()}`;
						break;
					}
				}
			}

			// Check if still on login page
			if (currentUrl.includes('/login/')) {
				errorMessage += ' (still on login page)';

				// Check for specific login issues
				const usernameField = await page.$(
					'input[name="username"], input[name="log"]',
				);
				const passwordField = await page.$(
					'input[name="password"], input[name="pwd"]',
				);

				if (usernameField && passwordField) {
					const usernameValue = await usernameField.inputValue();
					const passwordValue = await passwordField.inputValue();

					if (!usernameValue) {
						errorMessage += ' - Username field is empty';
					}
					if (!passwordValue) {
						errorMessage += ' - Password field is empty';
					}
				}
			}

			// Check for Cloudflare or other challenges still present
			const stillHasChallenge =
				(await page.$('.cf-turnstile')) ||
				(await page.$('[data-sitekey]')) ||
				(await page.$('iframe[src*="cloudflare"]'));

			if (stillHasChallenge) {
				errorMessage += ' - Cloudflare challenge may not be completed';
			}

			throw new Error(errorMessage);
		}

		// Save session state
		await browserManager.saveStorageState();

		logger.info('WPMU Dev login completed successfully');
		return {
			success: true,
			message: 'Login completed successfully',
			browserManager,
			requiresCleanup: shouldCloseBrowser,
		};
	} catch (error) {
		logger.error('WPMU Dev login failed', error);

		if (shouldCloseBrowser && browserManager) {
			await browserManager.close();
		}

		throw error;
	}
}

/**
 * Logout from WPMU Dev Hub
 * @param {WpmuBrowserManager} browserManager - Browser manager instance
 * @returns {Promise<Object>} Result object
 */
async function logoutFromWpmuDev(browserManager) {
	try {
		logger.info('Starting WPMU Dev logout process');

		const page = browserManager.getPage();

		// Navigate to hub if not already there
		await page.goto('https://wpmudev.com/hub2/', {
			waitUntil: 'networkidle',
			timeout: 15000,
		});

		// Look for user menu or logout option
		const userMenu =
			(await page.$('[data-test="user-menu"]')) ||
			(await page.$('.user-menu')) ||
			(await page.$('.account-menu'));

		if (userMenu) {
			await userMenu.click();
			await page.waitForTimeout(1000);

			const logoutLink =
				(await page.$('a[href*="logout"]')) ||
				(await page.$('button:has-text("Logout")')) ||
				(await page.$('button:has-text("Sign Out")'));

			if (logoutLink) {
				await logoutLink.click();
				await page.waitForTimeout(2000);
			}
		}

		// Clear storage state
		const contextPath = browserManager.contextPath;
		const storageStatePath = require('path').join(
			contextPath,
			'storage-state.json',
		);

		try {
			await require('fs').promises.unlink(storageStatePath);
			logger.info('Storage state cleared');
		} catch (error) {
			// File might not exist, which is fine
			logger.info('No storage state to clear');
		}

		logger.info('WPMU Dev logout completed');
		return {
			success: true,
			message: 'Logout completed successfully',
		};
	} catch (error) {
		logger.error('WPMU Dev logout failed', error);
		throw error;
	}
}

module.exports = {
	loginToWpmuDev,
	logoutFromWpmuDev,
};
