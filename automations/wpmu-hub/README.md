# WPMU Hub Automations

This directory contains automation blocks for WPMU Dev Hub operations with persistent browser context and session management.

## Features

- **Persistent Browser Context**: Sessions are saved to disk and reused across automation runs
- **Automatic Login Detection**: Smart detection of login status using multiple methods
- **2FA Support**: Handles two-factor authentication by pausing for user input
- **Session Management**: High-level utilities for managing authenticated sessions
- **Modular Design**: Login functionality separated into reusable blocks

## Files

### Core Components

- **`login.js`** - Standalone login automation block
- **`cloneSiteViaUI.js`** - Site cloning automation (updated to use session management)
- **`sessionExample.js`** - Example demonstrating session management usage

### Supporting Libraries

- **`../../lib/wpmuBrowserManager.js`** - Browser context and session management
- **`../../lib/wpmuSession.js`** - High-level session utilities

## Usage

### Basic Session Management

```javascript
const { withAuthenticatedSession } = require('../../lib/wpmuSession');

// Execute any WPMU task with automatic session handling
const result = await withAuthenticatedSession(async (browserManager) => {
  const page = browserManager.getPage();
  
  // Your WPMU automation code here
  await page.goto('https://wpmudev.com/hub2/sites/');
  // ... do something
  
  return { success: true };
});
```

### Manual Session Control

```javascript
const { getAuthenticatedSession } = require('../../lib/wpmuSession');

const browserManager = await getAuthenticatedSession({ headless: false });
try {
  const page = browserManager.getPage();
  // Your automation code here
} finally {
  await browserManager.close();
}
```

### Using the Login Block Directly

```javascript
const { loginToWpmuDev } = require('./login');

const result = await loginToWpmuDev({ 
  headless: false  // Set to false for 2FA support
});
```

## Session Detection

The system uses multiple methods to detect if you're logged in:

1. **URL Redirect Check**: Detects redirect to `/login/` page
2. **Login Form Detection**: Looks for `#wpmud-login-submit` element
3. **Authenticated Elements**: Checks for `.hub-navigation`, user menus, etc.

## 2FA (Two-Factor Authentication) Support

When 2FA is required:

1. The automation will detect the 2FA prompt
2. A message will be displayed asking you to complete 2FA in the browser
3. The automation waits for you to enter your 2FA code
4. Once completed, the automation continues automatically

**Important**: For 2FA to work, you must run automations with `headless: false`.

## Session Persistence

- Browser sessions are saved to `data/wpmu-context/storage-state.json`
- Sessions persist across automation runs
- Sessions are automatically validated before use
- Stale sessions (>24 hours) are considered invalid

## Examples

### Run the Example Script

```bash
node automations/wpmu-hub/sessionExample.js
```

This will:
1. Check for existing session
2. Login if needed (with 2FA support)
3. Navigate to WPMU Hub and gather basic info
4. Save the session for future use

### Clone a Site (Updated)

```javascript
const { cloneSiteViaUI } = require('./cloneSiteViaUI');

const result = await cloneSiteViaUI({
  city: 'NewYork',
  templateSite: 'wptemplate.impacthub.net',
  headless: false  // Set to false for 2FA support
});
```

## Configuration

### Environment Variables

The system uses the existing credential management:
- WPMU Dev credentials are retrieved via LastPass integration
- See `lib/credentials.js` for credential configuration

### Browser Options

- **headless**: `false` (default) for 2FA support, `true` for production
- **timeout**: Login timeout in milliseconds (default: 60000)
- **contextPath**: Custom path for storing browser context

## Troubleshooting

### Clear Stored Session

If you're having session issues:

```javascript
const { clearStoredSession } = require('../../lib/wpmuSession');
await clearStoredSession();
```

Or delete the file manually:
```bash
rm data/wpmu-context/storage-state.json
```

### 2FA Issues

- Ensure `headless: false` is set
- The browser window must remain open during 2FA
- Complete 2FA within the timeout period (default: 60 seconds)

### Login Detection Issues

If login detection fails, check:
- WPMU Dev site structure hasn't changed
- Network connectivity
- Credential validity

## Security Notes

- Browser context files contain session cookies
- Store context files securely
- Consider session timeout policies
- Clear sessions when no longer needed
