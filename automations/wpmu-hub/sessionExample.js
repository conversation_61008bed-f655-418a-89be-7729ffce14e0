/**
 * WPMU Hub - Session Management Example
 *
 * Example script demonstrating how to use the persistent browser context
 * and session management for WPMU Dev automations.
 */

// Load environment variables when running directly
require('dotenv').config();

const logger = require('../../lib/logger');
const {
	withAuthenticatedSession,
	hasStoredSession,
	clearStoredSession,
} = require('../../lib/wpmuSession');

/**
 * Example function that uses WPMU Dev session
 * @param {Object} options - Configuration options
 * @param {boolean} options.headless - Run in headless mode
 * @param {boolean} options.forceLogin - Force fresh login
 * @returns {Promise<Object>} Result object
 */
async function exampleWpmuTask(options = {}) {
	const { headless = false, forceLogin = false } = options;

	logger.info('Starting example WPMU task');

	try {
		// Check if we have a stored session
		const hasSession = await hasStoredSession();
		logger.info('Stored session status', { hasSession });

		// Execute task with authenticated session
		const result = await withAuthenticatedSession(
			async (browserManager) => {
				const page = browserManager.getPage();

				// Navigate to WPMU Dev Hub
				await page.goto('https://wpmudev.com/hub2/', {
					waitUntil: 'networkidle',
					timeout: 15000,
				});

				// Get some basic info about the current user/account
				const pageTitle = await page.title();
				const currentUrl = page.url();

				// Try to get user info if available
				let userInfo = null;
				try {
					const userElement =
						(await page.$('[data-test="user-menu"] .user-name')) ||
						(await page.$('.user-display-name')) ||
						(await page.$('.account-name'));

					if (userElement) {
						userInfo = await userElement.textContent();
					}
				} catch (error) {
					logger.info('Could not extract user info', error.message);
				}

				// Navigate to sites section to verify access
				await page.goto('https://wpmudev.com/hub2/sites/', {
					waitUntil: 'networkidle',
					timeout: 15000,
				});

				// Count sites if possible
				let siteCount = 0;
				try {
					const siteElements = await page.$$(
						'.site-item, .hub-site, [data-test="site-item"]',
					);
					siteCount = siteElements.length;
				} catch (error) {
					logger.info('Could not count sites', error.message);
				}

				return {
					pageTitle,
					currentUrl,
					userInfo,
					siteCount,
					timestamp: new Date().toISOString(),
				};
			},
			{ headless, forceLogin },
		);

		logger.info('Example WPMU task completed successfully', result);
		return {
			success: true,
			data: result,
			message: 'Example task completed successfully',
		};
	} catch (error) {
		logger.error('Example WPMU task failed', error);
		return {
			success: false,
			error: error.message,
			message: 'Example task failed',
		};
	}
}

/**
 * Clear session example
 * @returns {Promise<Object>} Result object
 */
async function clearSessionExample() {
	logger.info('Clearing stored WPMU session');

	try {
		await clearStoredSession();
		return {
			success: true,
			message: 'Session cleared successfully',
		};
	} catch (error) {
		logger.error('Failed to clear session', error);
		return {
			success: false,
			error: error.message,
			message: 'Failed to clear session',
		};
	}
}

// If this script is run directly, execute the example
if (require.main === module) {
	(async () => {
		try {
			console.log('🚀 Running WPMU Session Example...\n');

			// Run the example task
			const result = await exampleWpmuTask({ headless: false });

			console.log('\n📊 Results:');
			console.log(JSON.stringify(result, null, 2));

			if (result.success) {
				console.log('\n✅ Example completed successfully!');
				console.log('The browser session is now saved and can be reused.');
				console.log('Run this script again to see how it reuses the session.');
			} else {
				console.log('\n❌ Example failed!');
			}
		} catch (error) {
			console.error('\n💥 Unexpected error:', error);
			process.exit(1);
		}
	})();
}

module.exports = {
	exampleWpmuTask,
	clearSessionExample,
};
