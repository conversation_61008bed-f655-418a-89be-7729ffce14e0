/**
 * Credentials Configuration
 *
 * This file defines how to retrieve credentials from LastPass for different services.
 * No actual credentials are stored here - just the mapping to LastPass entries.
 */

const { getSecret } = require('../lib/lastpass');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

const CREDENTIAL_CONFIG = {
	wpmudev: {
		lastpassId: process.env.LASTPASS_WPMUDEV_ID,
		lastpassEntry: 'Shared-ImpactHub/WPMU Dev Login', // Fallback
		fields: {
			username: 'username', // Use standard --username flag
			password: 'password', // Use standard --password flag
		},
	},

	technologyEmail: {
		lastpassId: process.env.LASTPASS_TECHNOLOGY_EMAIL_ID,
		lastpassEntry: 'Tech Team/Google Accounts/Technology account', // Fallback
		fields: {
			email: 'username',
			password: 'password',
		},
	},

	notion: {
		lastpassId: process.env.LASTPASS_NOTION_API_ID,
		lastpassEntry: 'API Keys/Notion - Impact Hub', // Fallback
		fields: {
			apiKey: 'password', // API keys are typically stored in password field
		},
	},

	monday: {
		lastpassId: process.env.LASTPASS_MONDAY_API_ID,
		lastpassEntry: 'API Keys/Monday.com - Impact Hub', // Fallback
		fields: {
			apiKey: 'password',
		},
	},
};

/**
 * Get credentials for a specific service
 * @param {string} serviceName - The service name (e.g., 'wpmudev', 'notion')
 * @returns {Promise<Object>} Object with credential fields
 */
async function getCredentials(serviceName) {
	const config = CREDENTIAL_CONFIG[serviceName];

	if (!config) {
		throw new Error(
			`No credential configuration found for service: ${serviceName}`,
		);
	}

	const credentials = {};

	// Use ID if available, otherwise fall back to entry name
	const entryIdentifier = config.lastpassId || config.lastpassEntry;

	if (!entryIdentifier) {
		throw new Error(
			`No LastPass ID or entry name configured for service: ${serviceName}`,
		);
	}

	// Retrieve each configured field using standard LastPass CLI flags
	for (const [fieldName, lastpassField] of Object.entries(config.fields)) {
		if (lastpassField === 'username') {
			// Use --username flag for username
			const { stdout } = await execAsync(
				`lpass show --username "${entryIdentifier}"`,
			);
			credentials[fieldName] = stdout.trim();
		} else if (lastpassField === 'password') {
			// Use --password flag for password
			const { stdout } = await execAsync(
				`lpass show --password "${entryIdentifier}"`,
			);
			credentials[fieldName] = stdout.trim();
		} else {
			// Use --field for custom fields
			credentials[fieldName] = await getSecret(entryIdentifier, lastpassField);
		}
	}

	return credentials;
}

/**
 * Get specific credential field for a service
 * @param {string} serviceName - The service name
 * @param {string} fieldName - The field name (e.g., 'username', 'password', 'apiKey')
 * @returns {Promise<string>} The credential value
 */
async function getCredential(serviceName, fieldName) {
	const credentials = await getCredentials(serviceName);

	if (!(fieldName in credentials)) {
		throw new Error(
			`Field '${fieldName}' not found for service '${serviceName}'`,
		);
	}

	return credentials[fieldName];
}

module.exports = {
	getCredentials,
	getCredential,
	CREDENTIAL_CONFIG,
};
